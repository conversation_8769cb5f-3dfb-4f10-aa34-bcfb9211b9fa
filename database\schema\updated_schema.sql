-- Updated BeFine Database Schema for Supabase
-- This schema removes CIN fields and matches the Flutter models

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (base authentication table)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('patient', 'doctor')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Patients table (extends users)
CREATE TABLE IF NOT EXISTS patients (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    gender TEXT NOT NULL CHECK (gender IN ('male', 'female')),
    birthday DATE NOT NULL,
    height DECIMAL(5,2) NOT NULL CHECK (height > 0 AND height < 300),
    weight DECIMAL(5,2) NOT NULL CHECK (weight > 0 AND weight < 500),
    tel TEXT,
    smoking_status TEXT DEFAULT 'Unknown',
    blood_type TEXT DEFAULT 'Unknown',
    activity_level TEXT DEFAULT 'Unknown',
    sos_tel TEXT,
    notes TEXT,
    address TEXT DEFAULT 'Unknown',
    profile_image_path TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Doctors table (extends users)
CREATE TABLE IF NOT EXISTS doctors (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    gender TEXT NOT NULL CHECK (gender IN ('male', 'female')),
    phone_number TEXT,
    specialization TEXT DEFAULT 'Unknown',
    job_address TEXT DEFAULT 'Unknown',
    license_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Devices table
CREATE TABLE IF NOT EXISTS devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    device_id TEXT UNIQUE NOT NULL,
    device_type TEXT DEFAULT 'SmartInhealer',
    custom_name TEXT,
    ble_mac_address TEXT,
    battery_level INTEGER CHECK (battery_level >= 0 AND battery_level <= 100),
    storage_status TEXT DEFAULT 'normal' CHECK (storage_status IN ('normal', 'low', 'full')),
    is_connected BOOLEAN DEFAULT FALSE,
    connection_type TEXT DEFAULT 'none' CHECK (connection_type IN ('none', 'ble', 'wifi')),
    last_sync TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sprays table
CREATE TABLE IF NOT EXISTS sprays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    spray_name TEXT NOT NULL CHECK (spray_name IN ('Iprol', 'Aerol', 'Erva', 'Cortis', 'Raforex', 'Cyvax')),
    doses INTEGER NOT NULL CHECK (doses IN (200, 120, 100)),
    dose_type INTEGER NOT NULL CHECK (dose_type IN (12, 20, 25, 100, 125, 250)),
    color TEXT NOT NULL CHECK (color IN ('yellow', 'blue', 'red', 'jam', 'green', 'pink', 'purple')),
    left_doses INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Treatments table
CREATE TABLE IF NOT EXISTS treatments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    doctor_id UUID REFERENCES doctors(id) ON DELETE SET NULL,
    spray_id UUID REFERENCES sprays(id) ON DELETE CASCADE,
    uses_per_day INTEGER NOT NULL CHECK (uses_per_day > 0),
    doses_per_use INTEGER NOT NULL CHECK (doses_per_use > 0),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    duration_days INTEGER GENERATED ALWAYS AS (end_date - start_date) STORED,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_date_range CHECK (end_date > start_date)
);

-- Data metrics table (test results)
CREATE TABLE IF NOT EXISTS data_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    treatment_id UUID REFERENCES treatments(id) ON DELETE SET NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    co_level DECIMAL(8,3),
    pef DECIMAL(8,3),
    fev1 DECIMAL(8,3),
    fvc DECIMAL(8,3),
    fev1_fvc_ratio DECIMAL(5,3),
    fef2575 DECIMAL(8,3),
    respiratory_rate INTEGER,
    breath_temperature DECIMAL(5,2),
    breath_humidity DECIMAL(5,2),
    ambient_temperature DECIMAL(5,2),
    ambient_humidity DECIMAL(5,2),
    ambient_pressure DECIMAL(8,2),
    inhalation_count INTEGER,
    inhalation_duration DECIMAL(8,3),
    correct_technique BOOLEAN,
    dose_sync_status TEXT DEFAULT 'pending' CHECK (dose_sync_status IN ('pending', 'synced', 'failed')),
    dose_type INTEGER,
    battery_level INTEGER,
    flow_data BYTEA,
    notes TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_patients_name ON patients(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_doctors_name ON doctors(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_devices_patient_id ON devices(patient_id);
CREATE INDEX IF NOT EXISTS idx_devices_device_id ON devices(device_id);
CREATE INDEX IF NOT EXISTS idx_sprays_device_id ON sprays(device_id);
CREATE INDEX IF NOT EXISTS idx_treatments_patient_id ON treatments(patient_id);
CREATE INDEX IF NOT EXISTS idx_treatments_doctor_id ON treatments(doctor_id);
CREATE INDEX IF NOT EXISTS idx_treatments_active ON treatments(is_active);
CREATE INDEX IF NOT EXISTS idx_data_metrics_patient_id ON data_metrics(patient_id);
CREATE INDEX IF NOT EXISTS idx_data_metrics_device_id ON data_metrics(device_id);
CREATE INDEX IF NOT EXISTS idx_data_metrics_timestamp ON data_metrics(timestamp);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_patients_updated_at BEFORE UPDATE ON patients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_doctors_updated_at BEFORE UPDATE ON doctors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_devices_updated_at BEFORE UPDATE ON devices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sprays_updated_at BEFORE UPDATE ON sprays FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_treatments_updated_at BEFORE UPDATE ON treatments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE sprays ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view own record" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own record" ON users FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for patients table
CREATE POLICY "Patients can view own record" ON patients FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Patients can update own record" ON patients FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Doctors can view their patients" ON patients FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM treatments t 
        WHERE t.patient_id = patients.id 
        AND t.doctor_id = auth.uid()
    )
);

-- RLS Policies for doctors table
CREATE POLICY "Doctors can view own record" ON doctors FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Doctors can update own record" ON doctors FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for devices table
CREATE POLICY "Patients can manage own devices" ON devices FOR ALL USING (
    patient_id = auth.uid()
);

-- RLS Policies for sprays table
CREATE POLICY "Patients can manage sprays on own devices" ON sprays FOR ALL USING (
    EXISTS (
        SELECT 1 FROM devices d 
        WHERE d.id = sprays.device_id 
        AND d.patient_id = auth.uid()
    )
);

-- RLS Policies for treatments table
CREATE POLICY "Patients can view own treatments" ON treatments FOR SELECT USING (patient_id = auth.uid());
CREATE POLICY "Doctors can manage treatments for their patients" ON treatments FOR ALL USING (doctor_id = auth.uid());

-- RLS Policies for data_metrics table
CREATE POLICY "Patients can view own data" ON data_metrics FOR SELECT USING (patient_id = auth.uid());
CREATE POLICY "Patients can insert own data" ON data_metrics FOR INSERT WITH CHECK (patient_id = auth.uid());
CREATE POLICY "Doctors can view patient data" ON data_metrics FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM treatments t 
        WHERE t.patient_id = data_metrics.patient_id 
        AND t.doctor_id = auth.uid()
    )
);
