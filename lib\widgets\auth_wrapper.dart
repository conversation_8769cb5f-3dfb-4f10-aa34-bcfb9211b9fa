import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:befine/services/auth_service.dart';
import 'package:befine/pages/welcom/onboarding_screen.dart';
import 'package:befine/pages/patient/patient_main_page.dart';
import 'package:befine/pages/doctor/doctor_main_page.dart';
import 'package:befine/theme/app_theme.dart';

/// Authentication wrapper that handles initial app state and authentication
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitializing = true;
  bool _showOnboarding = true;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Check if onboarding has been shown
      final prefs = await SharedPreferences.getInstance();
      final hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

      // Initialize auth service
      if (mounted) {
        final authService = Provider.of<AuthService>(context, listen: false);
        await authService.initialize();

        if (mounted) {
          setState(() {
            _showOnboarding = !hasSeenOnboarding;
            _isInitializing = false;
          });
        }
      }
    } catch (e) {
      debugPrint('App initialization error: $e');
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return _buildLoadingScreen();
    }

    return Consumer<AuthService>(
      builder: (context, authService, child) {
        // Show onboarding if user hasn't seen it
        if (_showOnboarding) {
          return const OnboardingScreen();
        }

        // Show appropriate main screen based on authentication state
        if (authService.isAuthenticated) {
          if (authService.isPatient) {
            return const PatientMainPage();
          } else if (authService.isDoctor) {
            return const DoctorMainPage();
          }
        }

        // Default to onboarding if not authenticated
        return const OnboardingScreen();
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo or branding
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                Icons.health_and_safety,
                size: 60,
                color: AppTheme.primaryColor,
              ),
            ),

            const SizedBox(height: 32),

            Text(
              'BeFine',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'Smart Respiratory Health Management',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 48),

            // Loading indicator
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'Initializing...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
