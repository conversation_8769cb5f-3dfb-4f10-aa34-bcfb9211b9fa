import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase configuration class for BeFine application
class SupabaseConfig {
  // Supabase project configuration
  static const String supabaseUrl = 'https://ibjlwgzdeqgmqiwqcaml.supabase.co';
  // TODO: Replace with actual anon key from Supabase project settings
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY_HERE';

  /// Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce,
      ),
    );
  }

  /// Get Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;

  /// Get current user
  static User? get currentUser => client.auth.currentUser;

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;
}
