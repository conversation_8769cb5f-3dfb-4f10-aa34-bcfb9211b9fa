/// Patient model class for storing and managing patient information
/// Integrated with Supabase authentication and database
class PatientModel {
  /// User ID from Supabase auth
  final String id;

  /// Required fields
  final String firstName;
  final String lastName;
  final String gender;
  final String dateOfBirth;
  final double height;
  final double weight;
  final String phoneNumber;
  final String email;

  /// Optional fields with default values
  final String smokingStatus;
  final String bloodType;
  final String activityLevel;
  final String emergencyContact;
  final String notes;

  /// Location/Address fields
  final String address;

  /// Profile image path
  final String? profileImagePath;

  /// Timestamps
  final DateTime? createdAt;
  final DateTime? updatedAt;

  PatientModel({
    // Required fields
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.dateOfBirth,
    required this.height,
    required this.weight,
    required this.phoneNumber,
    required this.email,

    // Optional fields with default values
    this.smokingStatus = 'Unknown',
    this.bloodType = 'Unknown',
    this.activityLevel = 'Unknown',
    this.emergencyContact = 'Unknown',
    this.notes = 'Unknown',

    // Location/Address fields with default values
    this.address = 'Unknown',

    // Profile image path
    this.profileImagePath,

    // Timestamps
    this.createdAt,
    this.updatedAt,
  });

  /// Create PatientModel from Supabase JSON
  factory PatientModel.fromJson(Map<String, dynamic> json) {
    return PatientModel(
      id: json['id'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      gender: json['gender'] as String,
      dateOfBirth: json['date_of_birth'] as String,
      height: (json['height'] as num).toDouble(),
      weight: (json['weight'] as num).toDouble(),
      phoneNumber: json['phone_number'] as String? ?? '',
      email: json['email'] as String,
      smokingStatus: json['smoking_status'] as String? ?? 'Unknown',
      bloodType: json['blood_type'] as String? ?? 'Unknown',
      activityLevel: json['activity_level'] as String? ?? 'Unknown',
      emergencyContact: json['emergency_contact'] as String? ?? 'Unknown',
      notes: json['notes'] as String? ?? 'Unknown',
      address: json['address'] as String? ?? 'Unknown',
      profileImagePath: json['profile_image_path'] as String?,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'] as String)
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
    );
  }

  /// Convert PatientModel to JSON for Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'gender': gender,
      'date_of_birth': dateOfBirth,
      'height': height,
      'weight': weight,
      'phone_number': phoneNumber,
      'email': email,
      'smoking_status': smokingStatus,
      'blood_type': bloodType,
      'activity_level': activityLevel,
      'emergency_contact': emergencyContact,
      'notes': notes,
      'address': address,
      'profile_image_path': profileImagePath,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  /// Create a copy of PatientModel with updated fields
  PatientModel copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? gender,
    String? dateOfBirth,
    double? height,
    double? weight,
    String? phoneNumber,
    String? email,
    String? smokingStatus,
    String? bloodType,
    String? activityLevel,
    String? emergencyContact,
    String? notes,
    String? address,
    String? profileImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PatientModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      smokingStatus: smokingStatus ?? this.smokingStatus,
      bloodType: bloodType ?? this.bloodType,
      activityLevel: activityLevel ?? this.activityLevel,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      notes: notes ?? this.notes,
      address: address ?? this.address,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
