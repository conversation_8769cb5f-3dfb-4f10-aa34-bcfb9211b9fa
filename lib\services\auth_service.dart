import 'package:flutter/widgets.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:befine/config/supabase_config.dart';
import 'package:befine/models/patient_model.dart';
import 'package:befine/models/doctor_model.dart';

/// Authentication service for handling user authentication with Supabase
class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  PatientModel? _currentPatient;
  DoctorModel? _currentDoctor;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  PatientModel? get currentPatient => _currentPatient;
  DoctorModel? get currentDoctor => _currentDoctor;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => SupabaseConfig.client.auth.currentUser != null;
  bool get isPatient => _currentPatient != null;
  bool get isDoctor => _currentDoctor != null;
  String? get currentUserId => SupabaseConfig.client.auth.currentUser?.id;

  /// Initialize the auth service
  Future<void> initialize() async {
    _setLoading(true);
    try {
      // Check if user is already logged in
      final session = SupabaseConfig.client.auth.currentSession;
      if (session != null) {
        await _loadUserProfile(session.user.id);
      }
    } catch (e) {
      debugPrint('Auth initialization error: $e');
      _setError('Failed to initialize authentication');
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in with email and password
  Future<bool> signIn(
    String email,
    String password, {
    bool keepLoggedIn = false,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SupabaseConfig.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);

        // Save login preference
        if (keepLoggedIn) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('keepLoggedIn', true);
          await prefs.setString('userRole', isPatient ? 'patient' : 'doctor');
        }

        return true;
      }
      return false;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      debugPrint('Sign in error: $e');
      _setError('An unexpected error occurred during sign in');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up as patient
  Future<bool> signUpPatient({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String gender,
    required String dateOfBirth,
    required double height,
    required double weight,
    required String phoneNumber,
    String smokingStatus = 'Unknown',
    String bloodType = 'Unknown',
    String activityLevel = 'Unknown',
    String emergencyContact = 'Unknown',
    String notes = 'Unknown',
    String address = 'Unknown',
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Create auth user
      final response = await SupabaseConfig.client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        final userId = response.user!.id;

        // Create patient record
        await SupabaseConfig.client.from('patients').insert({
          'id': userId,
          'first_name': firstName,
          'last_name': lastName,
          'gender': gender,
          'date_of_birth': dateOfBirth,
          'height': height,
          'weight': weight,
          'phone_number': phoneNumber,
          'email': email,
          'smoking_status': smokingStatus,
          'blood_type': bloodType,
          'activity_level': activityLevel,
          'emergency_contact': emergencyContact,
          'notes': notes,
          'address': address,
        });

        await _loadUserProfile(userId);
        return true;
      }
      return false;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      debugPrint('Patient sign up error: $e');
      _setError('An unexpected error occurred during registration');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up as doctor
  Future<bool> signUpDoctor({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String gender,
    required String phoneNumber,
    String specialty = 'Unknown',
    String address = 'Unknown',
    int? licenseNumber,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Create auth user
      final response = await SupabaseConfig.client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        final userId = response.user!.id;

        // Create doctor record
        await SupabaseConfig.client.from('doctors').insert({
          'id': userId,
          'first_name': firstName,
          'last_name': lastName,
          'gender': gender,
          'phone_number': phoneNumber,
          'email': email,
          'specialty': specialty,
          'address': address,
          'license_number': licenseNumber,
        });

        await _loadUserProfile(userId);
        return true;
      }
      return false;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      debugPrint('Doctor sign up error: $e');
      _setError('An unexpected error occurred during registration');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Send password reset email
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await SupabaseConfig.client.auth.resetPasswordForEmail(email);
      return true;
    } on AuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      return false;
    } catch (e) {
      debugPrint('Password reset error: $e');
      _setError('An unexpected error occurred while sending reset email');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setLoading(true);
    try {
      await SupabaseConfig.client.auth.signOut();
      _currentPatient = null;
      _currentDoctor = null;

      // Clear saved login preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('keepLoggedIn');
      await prefs.remove('userRole');
    } catch (e) {
      debugPrint('Sign out error: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load user profile from database
  Future<void> _loadUserProfile(String userId) async {
    try {
      // Try to get patient data first
      try {
        final patientResponse =
            await SupabaseConfig.client
                .from('patients')
                .select()
                .eq('id', userId)
                .single();

        _currentPatient = PatientModel.fromJson(patientResponse);
        _currentDoctor = null;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
        return;
      } catch (e) {
        // Patient not found, try doctor
      }

      // Try to get doctor data
      try {
        final doctorResponse =
            await SupabaseConfig.client
                .from('doctors')
                .select()
                .eq('id', userId)
                .single();

        _currentDoctor = DoctorModel.fromJson(doctorResponse);
        _currentPatient = null;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
        return;
      } catch (e) {
        // Doctor not found either
      }

      // If we get here, user exists in auth but not in our tables
      throw Exception('User profile not found in database');
    } catch (e) {
      debugPrint('Load user profile error: $e');
      throw Exception('Failed to load user profile');
    }
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  void _setError(String error) {
    _errorMessage = error;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  void _clearError() {
    _errorMessage = null;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  String _getAuthErrorMessage(AuthException e) {
    switch (e.message) {
      case 'Invalid login credentials':
        return 'Invalid email or password';
      case 'Email not confirmed':
        return 'Please check your email and confirm your account';
      case 'User already registered':
        return 'An account with this email already exists';
      default:
        return e.message;
    }
  }
}
